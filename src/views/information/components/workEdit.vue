<template>
  <div class="edit-unify li-pop">
    <el-form :inline="true" :model="editForm" class="demo-form-inline" :rules="rules" ref="ruleForm">
      <el-form-item label="职位名称" class="w280" prop="positionTypeName">
        <el-input v-model="editForm.positionTypeName" placeholder="请输入或选择职位" suffix-icon="el-icon-caret-bottom"
          @focus="getexpectCareer()"></el-input>
        <seleCareer @confirm="confirmCareer" v-if="dialogVisible" :hideValue="editForm.positionTypeId"></seleCareer>
      </el-form-item>
      <el-form-item label="工作行业" class="w280 fr" prop="workIndustryName">
        <el-input v-model="editForm.workIndustryName" placeholder="请选择" suffix-icon="el-icon-caret-bottom"
          @focus="getexpectIndustry()"></el-input>
        <seleIndustry :hideValue="[editForm.workIndustryId]" @confirm="confirmIndustry" v-if="dialogVisible2"
          :maxCount="1"></seleIndustry>
      </el-form-item>

      <!-- <el-form-item label="职位名称" class="w280" prop="positionName">
        <el-select v-model="editForm.positionName" filterable allow-create placeholder="请输入职位名称"
          :remote-method="positionText" :loading="loadingSEl" remote>
          <el-option v-for="item in positionTextList" :key="item.id" :label="item.name" :value="item.name"
            class="select-search">
            <p class="tit">{{ item.name }}</p>
            <span class="sub">{{ item.fullname }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="职位等级" class="w280 fr" prop="positionLevelName">
        <el-select v-model="editForm.positionLevelName" placeholder="请选择职位等级">
          <el-option v-for="(p, index) in PositionGradeList" :key="index" :label="p.keywordName" :value="p.keywordID"
            @click="editForm.positionLevel = p.keywordID"></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item label="在职时间" required>
        <el-row class="row-bg">
          <el-col :span="10">
            <el-form-item prop="experienceStartTime">
              <el-date-picker v-model="editForm.experienceStartTime" type="month" placeholder="请选择" style="width: 100%"
                value-format="YYYY-MM" :disabled-date="disabledDate"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2">至</el-col>
          <el-col :span="10">
            <el-form-item>
              <el-date-picker v-model="editForm.experienceFinishTime" type="month" value-format="YYYY-MM"
                placeholder="请选择" style="width: 100%" :disabled-date="disabledDate"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-checkbox label="至今" name="isToThisDay" v-model="editForm.isToThisDay" class="totaday"></el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="公司名称" class="w280" prop="entName">
        <el-select v-model="editForm.entName" filterable placeholder="请输入公司名称" allow-create :remote-method="entNameText"
          :loading="loadingSEl" remote>
          <el-option v-for="(item, index) in entTextList" :key="index" :label="item" :value="item" class></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所在部门" class="w280 fr">
        <el-input v-model="editForm.department" placeholder="请填写所在部门"></el-input>
      </el-form-item>

      <!-- <el-form-item label="公司性质" class="w280" prop="enterprisePropertyName">
        <el-select v-model="editForm.enterprisePropertyName" placeholder="请选择公司性质" clearable>
          <el-option v-for="(p, index) in CompanyNatureList" :key="index" :label="p.keywordName" :value="p.keywordID"
            @click="editForm.enterpriseProperty = p.keywordID"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="工作方式" class="w280 fr" prop="workPropertyName">
        <el-select v-model="editForm.workPropertyName" placeholder="请选择工作方式" clearable>
          <el-option v-for="(p, index) in WorkTypeList" :key="index" :label="p.keywordName" :value="p.keywordID"
            @click="editForm.workProperty = p.keywordID"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司规模" class="w280" prop="enterpriseEmployeeNumberName">
        <el-select v-model="editForm.enterpriseEmployeeNumberName" placeholder="请选择公司规模" clearable>
          <el-option v-for="(p, index) in CompanyScaleList" :key="index" :label="p.keywordName" :value="p.keywordID"
            @click="editForm.enterpriseEmployeeNumber = p.keywordID"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="工作地点" class="w280" style="width: 100%" prop="workPlace">
        <el-row class="row-bg">
          <el-col :span="9">
            <el-input v-model="editForm.workPlace" placeholder="请输入工作地点"></el-input>
          </el-col>
          <el-col :span="2">
            <el-checkbox label="海外" name="jobSeekerAbroadExperience" v-model="editForm.jobSeekerAbroadExperience"
              class="totaday"></el-checkbox>
          </el-col>
        </el-row>
      </el-form-item> -->

      <el-form-item label="工作描述" style="width: 100%" prop="positionDescription" class="workDescription">
        <el-input v-model="editForm.positionDescription" type="textarea" maxlength="2000" show-word-limit clearable
          size="medium" class="JobDescription" placeholder="描述工作内容、职务、团队成就等……"></el-input>
        <div class="othersWrite">
          <el-popover v-model:visible="visible" placement="bottom" :width="451">
            <template #reference>
              <span @click="visible = true" class="how">别人怎么写？</span>
            </template>
            <div class="describe-popover">
              <h2 class="tit">项目描述</h2>
              <p class="doc-p">职务描述应提供与工作内容相关的细节，以便HR快速了解您之前的工作内容。</p>
              <p class="li">例：</p>
              <p>1.策划新版广西人才网个人后台，完成产品设计和UI设计工作</p>
              <p>2.负责广西人才网侧边栏UI设计工作</p>
              <p>3.在广西人才网销售组超额完成年度销售计划</p>
              <p>4.为多客户提供完善的业务办理指南，制定符合该企业的业务方案</p>
            </div>
          </el-popover>
        </div>
      </el-form-item>
      <el-form-item label="工作技能" class="w280" style="width: 100%" prop="keywordIds" required>
        <div class="jobSkill">
          <div class="addSkill"><el-button type="primary" round @click="getworkSkills" class="btn-addSkills"><i
                class="el-icon-plus el-icon--right"></i>添加</el-button>
          </div>
          <div class="sdr">
            <ul><li v-for="(i, index) in editForm.keywordIds" :key="index">{{i.keywordName}}<i
                                class="el-icon-close" @click="deleteitem(index)"></i></li></ul>
          </div>
          <seleworkSkill @confirm="confirmworkSkill" v-if="dialogVisibleSkill" :hideValue="editForm.keywordIds" :careerId="editForm.positionTypeId"></seleworkSkill>
        </div>
      </el-form-item>


      <div class="tc">
        <el-button plain type="primary" icon="el-icon-circle-plus-outline" @click="showsenior = true" class="btn"
          v-if="!showsenior">添加附加信息</el-button>
      </div>

      <div v-if="showsenior" class="extraMessage">
        <el-form-item label="汇报对象" class="w280">
          <el-input v-model="editForm.higherUp" placeholder="请输入汇报对象"></el-input>
        </el-form-item>
        <el-form-item label="下属人数" class="w280 fr">
          <el-input v-model="editForm.underlingNum" placeholder="请输入下属人数" @keyup="changeAmount()" maxlength="6"
            @mouseleave="changeAmount()"></el-input>
        </el-form-item>
        <el-form-item label="离职原因" class="w400-suffix">
          <el-input v-model="editForm.leavingReason" placeholder="请输入离职原因" maxlength="30" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="工作业绩" style="width: 100%">
          <el-input v-model="editForm.jobPerformance" type="textarea" maxlength="1000" show-word-limit clearable
            size="medium" class="JobDescription" placeholder="请描述您的工作业绩……"></el-input>
        </el-form-item>
        <p class="tc pack-up" @click="showsenior = false">
          <i class="el-icon-arrow-up"></i>
          收起附加信息
        </p>
      </div>

      <el-form-item class="btn-end" style="width: 100%">
        <el-button type="primary" @click="onSubmit" class="sub">保存</el-button>
        <el-button @click="cancel" class="cel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  ref,
  defineComponent,
  watch,
  onBeforeMount,
  Ref,
} from "vue";
import seleCareer from "@/components/seleCareer.vue";
import seleIndustry from "@/components/seleIndustry.vue";
import seleworkSkill from "@/components/seleworkSkill.vue";

import {
  getEntpropertyoptions,
  getPositionleveloptions,
  getWorkpropertyoptions,
  getEmployeenumberoptions,
} from "../../../http/dictionary";
import { getWork, saveWork } from "../../../http/resumeApi";
import { searchPosition, searchEnterpriseName } from "../../../http/searchAPI";
import { ElMessage, ElMessageBox } from "element-plus";
import { useStore } from "vuex";
export default defineComponent({
  components: { seleCareer, seleIndustry, seleworkSkill },
  emits: ["handleRtn"],
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    workid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      editForm: {
        workId: 0,
        resumeId: props.resumeid,
        entName: "",
        positionDescription: "",
        keywordIds: [],
        experienceStartTime: "",
        experienceFinishTime: "",
        jobSeekerAbroadExperience: false,
        enterpriseProperty: 0,
        enterprisePropertyName: "",
        higherUp: "",//回报对象
        underlingNum: 0,
        leavingReason: "",
        isToThisDay: false,
        jobPerformance: "",
        department: "",
        positionLevel: 0,
        positionLevelName: "",
        enterpriseEmployeeNumber: 0,
        enterpriseEmployeeNumberName: "",
        positionTypeId: 0,
        positionTypeName: "",
        workPlace: "",
        workProperty: 0,
        workPropertyName: "",
        workIndustryId: 0,
        workIndustryName: "",
        positionName: "",
        hideIt: true,
      } as any,
      address: "",
      showsenior: "",
      CompanyNatureList: "", //公司性质字典
      PositionGradeList: "", //职位等级字典
      WorkTypeList: "", //工作方式字典
      CompanyScaleList: "", //公司规模字典
      dialogVisible: false,
      dialogVisible2: false,
      dialogVisibleSkill:false,
      positionTextList: ref([]),
      entTextList: "",
      visible: ref(false),
      loadingSEl: false,
      disabledDate(time: any) {
        return time.getTime() > Date.now();
      },
    });
    let rules = {
      positionTypeName: [
        {
          required: true,
          message: "请输入或选择职位",
          trigger: "change",
        },
      ],
      workIndustryName: [
        {
          required: true,
          message: "请选择工作行业",
          trigger: "change",
        },
      ],
      // positionName: [
      //   {
      //     required: true,
      //     message: "请选择职位名称",
      //     trigger: "blur",
      //   },
      // ],
      experienceStartTime: [
        {
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      experienceFinishTime: [
        {
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
      entName: [
        {
          required: true,
          message: "请填写公司名称",
          trigger: "blur",
        },
      ],
      // positionLevelName: [
      //   {
      //     required: true,
      //     message: "请选择职位职位等级",
      //     trigger: "change",
      //   },
      // ],
      // enterpriseEmployeeNumberName: [
      //   {
      //     required: true,
      //     message: "请选择公司规模",
      //     trigger: "blur",
      //   },
      // ],
      // workPlace: [
      //   {
      //     required: true,
      //     message: "请输入工作地点",
      //     trigger: "blur",
      //   },
      // ],
      positionDescription: [
        {
          required: true,
          message: "请输入工作描述",
          trigger: "blur",
        },
      ],
      keywordIds: [
        {
          required: true,
          message: "请输入工作技能",
          trigger: "blur",
        },
      ],
      // enterprisePropertyName: [
      //   {
      //     required: true,
      //     message: "请选择公司性质",
      //     trigger: "blur",
      //   },
      // ],
      // workPropertyName: [
      //   {
      //     required: true,
      //     message: "请选择工作方式",
      //     trigger: "blur",
      //   },
      // ],
    };
    watch(
      () => state.editForm.isToThisDay,
      (newValue, oldValue) => {
        state.editForm.experienceFinishTime = state.editForm.isToThisDay ? '' : state.editForm.experienceFinishTime
      }
    );
    watch(
      () => state.editForm.experienceFinishTime,
      (newValue, oldValue) => {
        state.editForm.isToThisDay = state.editForm.experienceFinishTime ? false : true
      }
    );
    let prevent = 1;
    const methods = {
      //获取工作数据
      async getData() {
        let data = {
          resumeid: props.resumeid,
          workid: props.workid,
        };
        let res: any = await getWork(data);
        if (res.code == 1) {
          state.editForm = res.data;
          state.showsenior = res.data.higherUp || res.data.jobPerformance || res.data.leavingReason || res.data.underlingNum;
          state.editForm.experienceFinishTime = state.editForm.isToThisDay ? '' : state.editForm.experienceFinishTime;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      // 公司性质--字典
      async getCompanyNatureList() {
        let result = store.state.companyNatureList;
        if (result.length <= 0) {
          let res: any = await getEntpropertyoptions("");
          state.CompanyNatureList = res.data;
          store.commit("setCompanyNatureList", res.data);
        } else {
          state.CompanyNatureList = result;
        }
      },
      // 职位等级--字典
      async getPositionGradeList() {
        let result = store.state.positionGradeList;
        if (result.length <= 0) {
          let res: any = await getPositionleveloptions("");
          state.PositionGradeList = res.data;
          store.commit("setPositionGradeList", res.data);
        } else {
          state.PositionGradeList = result;
        }

      },
      // 工作方式--字典
      async getWorkTypeList() {
        let result = store.state.workTypeList;
        if (result.length <= 0) {
          let res: any = await getWorkpropertyoptions("");
          state.WorkTypeList = res.data;
          store.commit("setWorkTypeListt", res.data);
        } else {
          state.WorkTypeList = result;
        }

      },
      // 公司规模--字典
      async getCompanyScaleList() {
        let result = store.state.companyScaleList;
        if (result.length <= 0) {
          let res: any = await getEmployeenumberoptions("");
          state.CompanyScaleList = res.data;
          store.commit("setCompanyScaleList", res.data);
        } else {
          state.CompanyScaleList = result;
        }
      },
      //保存--工作
      async saveData() {
        if (prevent === 2) {
          return false
        }
        let form = state.editForm;
        prevent = 2;
        let res: any = await saveWork(form);
        if (res.code == 1) {
          ElMessage.success(res.message);
          store.commit("editorShow", 0);
          emit("handleRtn", 1);
        } else {
          ElMessage.error(res.message);
        }
        prevent = 1;
      },
      async debounce(text: any) {
        let data: any = await searchPosition(text);
        if (data.code == 1 && data.data.length > 0) {
          state.positionTextList = data.data;
        }
      },
      //搜索--公司名称
      async getEntNameText(text: any) {
        let data: any = await searchEnterpriseName(text);
        if (data.code == 1 && data.data.count > 0) {
          state.entTextList = data.data.result;
        }
      },
    };

    onBeforeMount(() => {
      //获取数据
      if (props.workid > 0) {
        methods.getData();
      }
      methods.getCompanyNatureList();
      methods.getPositionGradeList();
      methods.getWorkTypeList();
      methods.getCompanyScaleList();
    });
    const fun = {
      //取消编辑-不做任何处理
      cancel() {
        emit("handleRtn", 2);
        store.commit("editorShow", 0);
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        if ((state.editForm.experienceStartTime > state.editForm.experienceFinishTime) && !state.editForm.isToThisDay) {
          ElMessage.success("在职时间的开始时间不得晚于结束时间")
          return false
        }
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          }
        });
      },
      //获取职位字典--弹窗
      getexpectCareer() {
        state.dialogVisible = true;
      },
      //获取期望行业--弹窗
      getexpectIndustry() {
        state.dialogVisible2 = true;
      },
      //获取工作技能字典--弹窗
      getworkSkills() {
        if(!state.editForm.positionTypeId){
          ElMessage({
              showClose: true,
              message: `请选择职位类型`,
              type: "warning",
          });
             return false
          }
        state.dialogVisibleSkill = true;
      },
      //接收从子集传过来的数据
      confirmCareer(p: any) {
        state.dialogVisible = false;
        if (!p) {
          return false;
        }
        state.editForm.positionTypeName = p.keywordName;
        state.editForm.positionTypeId = p.keywordID;
        state.editForm.keywordIds=[];
      },
      //传过来的工作技能
      confirmworkSkill(arr:any,type:number){
        console.log("传过来的工作技能",arr)
        state.dialogVisibleSkill = false;
        if(type==1){
          state.editForm.keywordIds = arr;
        }
      },
      deleteitem(ind:number) {
        state.editForm.keywordIds.splice(ind, 1)
      },
      //接收从子集传过来的数据
      confirmIndustry(p: any) {
        state.dialogVisible2 = false;
        if (!p) {
          return false;
        }
        state.editForm.workIndustryName = p[0].keywordName;
        state.editForm.workIndustryId = p[0].keywordID;
      },

      positionText(query: string) {
        if (query) {
          state.loadingSEl = true
          setTimeout(() => {
            methods.debounce(query);
            state.loadingSEl = false
          }, 200)
        } else {
        }
      },


      entNameText(query: string) {
        if (query) {
          state.loadingSEl = true
          setTimeout(() => {
            methods.getEntNameText(query);
            state.loadingSEl = false
          }, 200)
        } else {
        }
      },
      changeAmount() {
        state.editForm.underlingNum = state.editForm.underlingNum.replace(/[^\d.]/g, '')
      }
    };

    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>

<style lang="less">
.workComponent {
  .li-pop {
    .el-form-item__label {
      width: 90px;
      text-align: left;
    }

    .el-form {
      .fr {
        margin-right: 0;
      }
    }

    .JobDescription {
      .el-textarea__inner {
        height: 130px;
      }
    }

    .btn {
      color: #457ccf;
      background: #fff;
      border: 1px solid #457ccf;
      font-size: 14px;
    }

    .extraMessage {
      padding: 20px 0 0 0;
      position: relative;
    }

    .extraMessage::before {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: -24px;
      top: 0;
      left: -20px;
      border-bottom: 1px solid #f2f2f2;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }

    .pack-up {
      font-size: 14px;
      color: #999;
      cursor: pointer;
      padding: 0 0 40px 0;
    }
  }

  .othersWrite {
    top: 130px;
  }
}

.select-search.el-select-dropdown__item {
  height: auto;
  line-height: normal;
  padding: 10px 24px;

  .tit {
    color: #457ccf;
    font-size: 14px;
  }

  span.sub {
    color: #999;
    font-size: 12px;
  }
}

.jobSkill {
  display: flex;
  .btn-addSkills{
    height: 35px;
    line-height: 35px;
    padding: 0 20px;
  }
  .sdr{
    flex:1;
    ul{
      li{
        float: left;
        height: 35px;
        line-height: 35px;
        padding: 0 20px;
        background: #ffffff;
        border-radius: 30px;
        margin-left: 10px;
        margin-bottom: 5px;
        i{
          cursor: pointer;
          padding-left: 2px;
        }
        
      }
    }
  }
}
</style>