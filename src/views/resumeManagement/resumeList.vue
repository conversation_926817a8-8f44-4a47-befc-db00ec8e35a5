<template>
  <div class="resumeList-mn">
    <subheading :step="2"></subheading>
    <!-- table--star -->
    <div class="table">
      <el-table :data="resumeList" style="width: 100%">
        <el-table-column label="简历名称" width="180">
          <template #default="scope">
            <el-tooltip
              :content="scope.row.resumeName"
              placement="top"
              effect="light"
            >
              <div class="name">{{ scope.row.resumeName }}</div>
            </el-tooltip>

            <p class="time">{{ scope.row.lastEditTime }}最后编辑</p>
          </template>
        </el-table-column>
        <el-table-column label="简历完整度" width="180">
          <template #default="scope">
            <el-rate
              v-model="scope.row.stars"
              disabled
              allow-half
              text-color="#FDAA08"
              void-color="#999999"
              v-if="scope.row.stars > 1"
            >
            </el-rate>
            <p class="Noaccomplish" v-if="scope.row.resumeState == 0">
              简历未完成
            </p>
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row.notPassResult"
              placement="top-start"
              v-if="scope.row.resumeState == 3"
            >
              <el-button class="Nopass"> 审核未通过<br />查看原因</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="被看数量" width="220">
          <template #default="scope">
            <p>
              <label class="blue">{{ scope.row.hitsCount }}</label> 家企业看过
            </p>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <span
              class="min-btn"
              @click="
                methods.setPrivates(scope.row.resumeId, scope.row.publicState)
              "
            >
              <i
                :class="
                  scope.row.publicState == 2
                    ? 'blue iconfont icon-jianligongkai'
                    : 'iconfont icon-lock7'
                "
              ></i>
              <br />
              {{ scope.row.publicState == 2 ? "公开" : "隐藏" }}
            </span>
            <a
              class="min-btn"
              :href="'/resume/' + scope.row.resumeId"
              target="_blank"
            >
              <i class="iconfont icon-bianji1"></i>
              <br />
              编辑
            </a>
            <span
              class="min-btn"
              @click="
                methods.postSetDefaultResume(
                  scope.row.resumeId,
                  scope.row.isDefault
                )
              "
              style="width: 50px"
            >
              <i
                :class="['iconfont icon-moren', { blue: scope.row.isDefault }]"
              ></i>
              <br />
              {{ scope.row.isDefault ? "默认" : "设为默认" }}
            </span>
            <a
              class="min-btn"
              :href="'/preview/' + scope.row.resumeId"
              target="_blank"
              rel="noopener noreferrer"
            >
              <i class="iconfont icon-liulan"></i>
              <br />
              预览
            </a>
            <span
              class="min-btn"
              @click="del(scope.row.resumeId, scope.row.isDefault)"
            >
              <i class="iconfont icon-shanchu"></i>
              <br />
              删除
            </span>
            <el-button
              size="mini"
              round
              class="freshenBtn"
              @click="methods.postRefreshresume(scope.row.resumeId)"
              >刷新简历</el-button
            >
          </template>
        </el-table-column>
        <template #empty>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </el-table>
    </div>

    <div class="btn-wrap" v-if="resumeListCount < 2">
      <el-button
        round
        @click="methods.addresume"
        :disabled="resumeListCount >= 2"
        >创建新简历</el-button
      >
    </div>

    <!-- table--end -->
    <div class="appendix">
      <div class="clearfix">
        <div class="sdl">
          <h1>
            <i class="iconfont icon-wenjianshangchuan"></i> 简历附件({{
              attachmentList.length
            }}/3)
          </h1>
          <p>向企业展示您更多的个人信息</p>
        </div>
        <div class="sdr">
          <el-button round @click="uploadResumeAttachment()"
            >管理简历附件</el-button
          >
        </div>
      </div>

      <div>
        <ul class="attachment-list">
          <li
            class="clearfix"
            v-for="(item, index) in attachmentList"
            :key="index"
          >
            <div class="attachment-item">
              <div class="attachment-item-left">
                <div @click="methods.getAttachmentPreview(item.id)">
                  <el-image :src="item.icon" fit="fill"></el-image>
                  <span class="tit">{{ item.attachmentDisplayName }}</span>
                </div>
                <el-popover
                  v-if="item.resumeAttachmentStatus != 1"
                  :popper-class="
                    item.resumeAttachmentStatus == 0
                      ? 'attachment-dsh'
                      : 'attachment-btg'
                  "
                  placement="bottom"
                  :width="200"
                  trigger="hover"
                  :content="item.auditMessage"
                >
                  <template #reference>
                    <div
                      class="state"
                      :class="item.resumeAttachmentStatus == 0 ? 'dsh' : 'btg'"
                    >
                      {{ item.resumeAttachmentStatusName }}
                    </div>
                  </template>
                </el-popover>
                <div
                  class="state dsh"
                  v-if="
                    item?.resumeAttachmentStatus != 2 && item?.isCanAnalysis
                  "
                >
                  可解析
                </div>
              </div>
              <div
                class="sync"
                v-if="item?.resumeAttachmentStatus != 2 && item?.isCanAnalysis"
                @click="methods.syncToOnlineResume(item.id)"
              >
                同步至在线简历
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <UploadResumeAttachment
    :dialogResumeAttachmentVisible="dialogResumeAttachmentVisible"
    :attachmentList="attachmentList"
    @closedResumeAttachment="closedResumeAttachment"
  />
  <MymallRefreshResumeAuto :drawer="rraDrawer" />
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  computed,
  onMounted,
  ref,
} from "vue";
import {
  getResumeList,
  setDefaultResume,
  refreshResume,
  attachmentlist,
  attachmentPreview,
} from "@/http/api";
import { setprivate, addResume, deleteReusme } from "@/http/resumeApi";
import { useRouter, useRoute } from "vue-router";
import subheading from "@/views/resumeManagement/subheading.vue";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import UploadResumeAttachment from "@/components/UploadResumeAttachment.vue";
import MymallRefreshResumeAuto from "@/components/MymallRefreshResumeAuto.vue";

export default defineComponent({
  components: { subheading, UploadResumeAttachment, MymallRefreshResumeAuto },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      resumeList: ref([]),
      resumeListCount: 0,
      num: 1,
      attachmentList: [],
      dialogResumeAttachmentVisible: { value: false },
      rraDrawer: { value: false },
    });
    onMounted(() => {
      methods.getResumeList();
      methods.getAttachmentlist();
    });
    const methods = {
      async getResumeList() {
        const res = await getResumeList();
        state.resumeList = res.data.resumeList;
        state.resumeListCount = res.data.resumeList.length;
      },
      // 设置隐私
      async setPrivates(resumeId: number, PublicState: number) {
        // 2全部企业可见   3---仅投递可见
        let num = PublicState == 2 ? 3 : 2;
        let form = {
          resumeId: resumeId,
          resumePublicState: num,
        };
        let data = await setprivate(form);
        if (data.code == 1) {
          if (num == 3) {
            ElMessage.success("保存成功，当前仅投递企业可见");
          } else {
            ElMessage.success(data.message);
          }
          // ElMessage.success(data.message);
          methods.getResumeList();
        } else {
          ElMessage.error(data.message);
        }
      },
      //设置默认
      async postSetDefaultResume(resumeId: number, isDefault: boolean) {
        if (isDefault) {
          ElMessage.warning("该简历已是默认简历");
          return false;
        }
        const res = await setDefaultResume(resumeId);
        if (res.code == 1) {
          ElMessage.success(res.message);
          methods.getResumeList();
        } else {
          ElMessage.error(res.message);
        }
      },
      //   删除
      async del(resumeId: number) {
        let form = {
          resumeid: resumeId,
          isdel: true,
        };
        let data = await deleteReusme(form);
        if (data.code == 1) {
          ElMessage.success(data.message);
          methods.getResumeList();
        } else {
          ElMessage.error(data.message);
        }
      },
      //刷新简历
      async postRefreshresume(resumeId: number) {
        const res = await refreshResume(resumeId);
        if (res.code == 1) {
          ElMessage.success(res.message);
        } else {
          ElMessage.error(res.message);
          state.rraDrawer.value = true;
        }
      },
      //添加简历
      async addresume() {
        if (state.resumeListCount >= 2) {
          ElMessage.warning("温馨提示:您已经创建了两份简历");
        } else {
          const loading = ElLoading.service({
            lock: true,
            text: "创建中...",
            background: "rgba(0, 0, 0, 0.7)",
          });

          let data = await addResume(0);
          loading.close();
          if (data.code == 1) {
            let resumeId = data.data.resumeId;
            let url = `/resume/${resumeId}`;
            window.open(url, "_blank");
            methods.getResumeList();
            // router.push({ path: `/resume/${resumeId}` });
            ElMessage.success(data.message);
          } else {
            ElMessage.error(data.message);
          }
        }
      },
      async getAttachmentlist() {
        const res = await attachmentlist();
        state.attachmentList = res.data;
      },
      async getAttachmentPreview(id: any) {
        const res = await attachmentPreview({ id: id });
        if (res.code == 1) {
          window.open(res.data, "_blank"); //todo  跳出去的链接不显示文档，显示xml
        } else {
          ElMessage.error(res.message);
        }
      },
      syncToOnlineResume(id: any) {
        router.push(`/resumeanalysis/${id}`);
      },
    };

    const fun = {
      del(resumeId: number, isDefault: boolean) {
        if (isDefault) {
          ElMessage.warning("温馨提示:默认简历不能删除");
        } else {
          ElMessageBox.confirm(
            "您确定要删除该份简历吗？删除后简历将永久删除。",
            "温馨提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              methods.del(resumeId);
            })
            .catch(() => {});
        }
      },
      // goURL(url: string) {
      //   router.push({ path: url });
      // },
      uploadResumeAttachment() {
        state.dialogResumeAttachmentVisible.value = true;
      },
      closedResumeAttachment(val: any) {
        if (val.listIsChange) {
          state.attachmentList = val.list;
        }
      },
    };

    return { ...toRefs(state), ...fun, methods };
  },
});
</script>
<style lang="less">
.resumeList-mn {
  .table {
    margin: 12px 0 0 0;
    .has-gutter th {
      text-align: center;
      font-size: 12px;
      color: #999999;
      font-weight: 400;
    }
    .name {
      font-size: 14px;
      color: #333;
      padding-left: 10px;
      width: 145px;
      height: 41px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      font-weight: 600;
    }
    .time {
      font-size: 12px;
      color: #999999;
      padding-left: 10px;
    }
    .el-table_1_column_2,
    .el-table_1_column_3,
    .el-table_3_column_11,
    .el-table_3_column_12 {
      text-align: center;
    }
    .el-table_1_column_4 {
      text-align: right;
    }
    .Noaccomplish {
      color: #fdaa08;
      font-size: 14px;
    }
    .Nopass {
      font-size: 14px;
      color: #fc5c5b;
      cursor: pointer;
      border: none;
    }
    .min-btn {
      display: inline-block;
      text-align: center;
      font-size: 12px;
      color: #666;
      padding: 0 15px;
      cursor: pointer;
    }
    .freshenBtn {
      float: right;
      margin-top: 10px;
      color: #5f9efc;
      border-color: #5f9efc;
    }
    .el-rate__icon {
      margin: 0 -2px 0 -2px;
    }
    .icon-moren {
      color: #ddd;
    }
    i.blue {
      color: #457ccf;
    }
  }
  .appendix {
    background: #fff;
    padding: 0 20px 20px;
    margin: 12px 0 0 0;
    .sdl {
      float: left;
    }
    .sdr {
      float: right;
    }
    h1 {
      font-size: 18px;
      color: #333;
      padding: 20px 0 8px 0;
      font-weight: 500;
      i {
        color: #5f9efd;
        font-size: 19px;
      }
    }
    p {
      font-size: 12px;
      color: #999999;
    }
    .el-button {
      color: #457ccf;
      border: 1px solid #457ccf;
      font-size: 14px;
      margin: 30px 0 0 0;
      background: #fff;
      padding: 0 0;
      height: 32px;
      line-height: 32px;
      width: 116px;
      text-align: center;
    }
  }
  .btn-wrap {
    padding: 20px;
    background: #fff;
    text-align: center;
  }
  .attachment-list {
    li {
      cursor: pointer;
      border-bottom: 1px solid #f2f2f2;
      padding: 15px 15px 15px 0;
      .attachment-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .attachment-item-left {
          display: flex;
          align-items: center;
        }
      }
      .el-image {
        vertical-align: middle;
      }
      img {
        width: 48px;
      }
      div.tit {
        font-size: 16px;
        color: #333;
      }
      div.state {
        display: inline-block;
        font-size: 12px;
        margin-left: 5px;
        color: #f1aa59;
        padding: 3px 5px;
        background: #fff4e6;
        border-radius: 2px;
      }
      .state.dsh {
        color: #f1aa59;
        background: #fff4e6;
      }
      .state.btg {
        color: #fe5c5b;
        background: #ffeaea;
      }
    }
    li:last-child {
      border-bottom: none;
    }
  }
  .sync {
    color: #457ccf;
    cursor: pointer;
    font-size: 14px;
  }
}
</style>
